# ESP32-C3 多功能遥控信号处理器使用说明

本项目基于ESP32-C3开发板，支持三种不同的遥控信号处理模式。通过PlatformIO的环境配置，可以选择编译不同的功能。

## 支持的功能

### 1. SBUS输入转PWM输出 (默认模式)

- **环境名称**: `sbus_in`
- **源文件**: `src/main.cpp`
- **功能**: 读取SBUS信号，将通道13和14转换为PWM输出
- **用途**: 控制舵机、电机等设备

### 2. IBUS信号读取和显示

- **环境名称**: `ibus_in`
- **源文件**: `src/main_ibus_in.cpp`
- **功能**: 读取富斯IA6B接收机的IBUS信号，通过串口打印所有通道数据
- **用途**: 调试IBUS信号，监控遥控器输入

### 3. SBUS转IBUS信号转换

- **环境名称**: `sbus_to_ibus`
- **源文件**: `src/main_sbus_to_ibus.cpp`
- **功能**: 读取SBUS信号并转换成IBUS格式输出
- **用途**: 将SBUS接收机信号转换为IBUS格式，供支持IBUS的设备使用

## 编译和上传

### 使用PlatformIO命令行

```bash
# 编译SBUS输入转PWM输出 (默认)
pio run -e sbus_in

# 编译IBUS信号读取
pio run -e ibus_in

# 编译SBUS转IBUS转换器
pio run -e sbus_to_ibus

# 上传到开发板 (以IBUS读取为例)
pio run -e ibus_in -t upload

# 监控串口输出
pio device monitor
```

### 使用VSCode PlatformIO插件

1. 打开VSCode，确保安装了PlatformIO插件
2. 在底部状态栏选择对应的环境：
   - `sbus_in` - SBUS输入转PWM输出
   - `ibus_in` - IBUS信号读取
   - `sbus_to_ibus` - SBUS转IBUS转换
3. 点击"Build"按钮编译
4. 点击"Upload"按钮上传到开发板

## 引脚连接

### ESP32-C3引脚定义

```
SBUS_RX_PIN = 7    // SBUS信号输入
SBUS_TX_PIN = 6    // SBUS信号输出 (未使用)
IBUS_RX_PIN = 7    // IBUS信号输入 (与SBUS共用)
IBUS_TX_PIN = 6    // IBUS信号输出
LED_PIN = 8        // 状态指示LED
PWM1_PIN = 3       // PWM输出1
PWM2_PIN = 4       // PWM输出2
```

### 连接说明

#### SBUS输入模式

- 将SBUS接收机的信号线连接到GPIO7
- PWM输出从GPIO3和GPIO4输出

#### IBUS输入模式

- 将富斯IA6B接收机的IBUS线连接到GPIO7
- 如需双向通信，GPIO6连接到IBUS线（需要二极管隔离）

#### SBUS转IBUS模式

- SBUS输入：GPIO7
- IBUS输出：GPIO3 (TX)
- 可以同时连接SBUS接收机和IBUS设备

## 串口监控

所有模式都支持115200波特率的串口调试输出：

### IBUS读取模式输出示例

```
IBUS Channels: CH1:1500 CH2:1500 CH3:1000 CH4:1500 CH5:1500 CH6:1500 CH7:1500 CH8:1500 CH9:1500 CH10:1500 | RX Count: 123
```

### SBUS转IBUS模式输出示例

```
SBUS->IBUS: CH1:372->1000 CH2:1001->1500 CH3:1630->2000 CH4:1001->1500 CH5:1001->1500 CH6:1001->1500
```

## 库依赖

项目自动管理以下库依赖：

- **Bolder Flight Systems SBUS** (v8.1.4): SBUS协议处理
- **IBusBM** (v1.1.4): IBUS协议处理

## 故障排除

### 编译错误

1. 确保选择了正确的环境
2. 检查库依赖是否正确安装
3. 清理构建缓存：`pio run -t clean`

### 无信号输入

1. 检查引脚连接是否正确
2. 确认接收机已正确绑定遥控器
3. 检查波特率设置（SBUS: 100000, IBUS: 115200）

### 串口无输出

1. 确认串口波特率设置为115200
2. 检查USB连接
3. 在Windows上可能需要安装CH340驱动

## 技术参数

- **开发板**: Seeed Studio XIAO ESP32C3
- **工作电压**: 3.3V
- **SBUS波特率**: 100000 bps, 8E2
- **IBUS波特率**: 115200 bps, 8N1
- **PWM频率**: 1000Hz
- **PWM分辨率**: 12位
- **通道范围**: 1000-2000μs (标准遥控范围)
