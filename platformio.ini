; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

; 默认环境 - SBUS输入转PWM输出
[env:sbus_in]
platform = espressif32
board = seeed_xiao_esp32c3
framework = arduino
board_build.f_cpu = 160000000L
lib_deps =
	; fastled/FastLED@^3.8.0
	bolderflight/Bolder Flight Systems SBUS@^8.1.4
extra_scripts =
	merge_firmware.py
build_flags =
	-D ARDUINO_USB_MODE=1
	-D ARDUINO_USB_CDC_ON_BOOT=1
src_filter = +<*> -<main_ibus_in.cpp> -<main_sbus_to_ibus.cpp> -<main_sbus_in.cpp>

; IBUS输入读取环境
[env:ibus_in]
platform = espressif32
board = seeed_xiao_esp32c3
framework = arduino
board_build.f_cpu = 160000000L
lib_deps =
	; fastled/FastLED@^3.8.0
	bolderflight/Bolder Flight Systems SBUS@^8.1.4
	bmellink/IBusBM@^1.1.5
extra_scripts =
	merge_firmware.py
build_flags =
	-D ARDUINO_USB_MODE=1
	-D ARDUINO_USB_CDC_ON_BOOT=1
src_filter = +<*> -<main.cpp> -<main_sbus_to_ibus.cpp> -<main_sbus_in.cpp>

; SBUS转IBUS环境
[env:sbus_to_ibus]
platform = espressif32
board = seeed_xiao_esp32c3
framework = arduino
board_build.f_cpu = 160000000L
lib_deps =
	; fastled/FastLED@^3.8.0
	bolderflight/Bolder Flight Systems SBUS@^8.1.4
	bmellink/IBusBM@^1.1.4
extra_scripts =
	merge_firmware.py
build_flags =
	-D ARDUINO_USB_MODE=1
	-D ARDUINO_USB_CDC_ON_BOOT=1
src_filter = +<*> -<main.cpp> -<main_ibus_in.cpp> -<main_sbus_in.cpp>
