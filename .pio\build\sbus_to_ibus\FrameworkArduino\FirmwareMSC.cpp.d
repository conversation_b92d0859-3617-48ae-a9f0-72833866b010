.pio/build/sbus_to_ibus/FrameworkArduino/FirmwareMSC.cpp.o: \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/FirmwareMSC.cpp \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/FirmwareMSC.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/USBMSC.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/qio_qspi/include/sdkconfig.h
