name=Bolder Flight Systems SBUS
version=8.1.4
author=<PERSON> <<EMAIL>>
maintainer=<PERSON> <<EMAIL>>
sentence=Library for communicating with SBUS receivers and servos.
paragraph=This library communicates with SBUS receivers and servos, and works with Teensy 3.x, 4.x, and LC devices, the STM32L4, and ESP32 or any device using a hardware serial inverter.
category=Device Control
url=https://github.com/bolderflight/sbus
architectures=*
includes=sbus.h
