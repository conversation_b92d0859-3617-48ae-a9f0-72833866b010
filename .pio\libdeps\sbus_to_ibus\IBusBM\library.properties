name=IBusBM
version=1.1.4
author=<PERSON>
maintainer=<PERSON>
sentence=<PERSON><PERSON><PERSON>o library for the Flysky/Turnigy RC iBUS protocol - servo (receive) and sensors/telemetry (send) using hardware UART (AVR, ESP32 and STM32 architectures)
paragraph=With this library you can interface to any RC receiver that supports the Flysky iBUS protocol (such as TGY-IA6B). Flysky iBUS uses a half-duplex asynchronous protocol format at 115200 baud. The library requires at least one free hardware UART (serial) port. The library can be used to receive data (typically servo data) and send data (telemetry or sensors).
category=Communication
url=https://github.com/bmellink/IBusBM
architectures=avr,esp32,stm32,mbed
includes=IBusBM.h
