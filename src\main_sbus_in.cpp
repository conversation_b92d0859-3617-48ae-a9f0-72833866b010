#include <Arduino.h>
#include <HardwareSerial.h>
#include "sbus.h"

typedef struct
{
    int8_t sbus_rx_pin;
    int8_t sbus_tx_pin;
    int8_t led_pin;
    int8_t pwm1_pin;
    int8_t pwm2_pin;

} PinDefinition;

// 结构体实例化
PinDefinition pin_definition = {
    .sbus_rx_pin = 7,
    .sbus_tx_pin = 6,
    .led_pin = 8,
    .pwm1_pin = 3,
    .pwm2_pin = 4};

// 定义常量
const int OUTPUT_FREQ = 1000;     // 输出PWM频率（Hz）
const int OUTPUT_RESOLUTION = 12; // 输出PWM分辨率（位）
const float PWM_DUTY = 0.45;      // 占空比

HardwareSerial SerialPrint(0);
HardwareSerial SerialSbus(1);

const int TARGET_MIN = 1000;
const int TARGET_MAX = 2000;
const int TARGET_MID = (TARGET_MAX - TARGET_MIN) / 2 + TARGET_MIN;
const int DEAD_ZONE = 10; // 中位死区
const int ch_max = 1630;
const int ch_min = 372;
const int ch_mid = (ch_max - ch_min) / 2 + ch_min;

bfs::SbusRx sbus_rx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusData sbus_data;

int pwm1_value = 0;
int pwm2_value = 0;

// 限幅函数
int valueClamp(int value, int minValue, int maxValue)
{
    if (value < minValue)
    {
        return minValue;
    }
    else if (value > maxValue)
    {
        return maxValue;
    }
    else
    {
        return value;
    }
}

// 定义一个函数来处理通道值的死区
int applyDeadZone(int pwm_ch)
{
    if (pwm_ch < TARGET_MIN + DEAD_ZONE)
    {
        return TARGET_MIN; // 如果小于死区，返回最小值
    }
    return pwm_ch; // 否则返回原值
}

void setup()
{
    /* Serial to display data */
    SerialPrint.begin(115200, SERIAL_8N1, -1, -1); // RX, TX
    // Serial.begin(115200);
    pinMode(pin_definition.led_pin, OUTPUT); // 设置LED引脚为输出模式

    /* Begin the SBUS communication */
    sbus_rx.Begin();

    // 初始化LEDC
    ledcSetup(0, OUTPUT_FREQ, OUTPUT_RESOLUTION); // 通道0，频率1kHz，12位分辨率
    ledcAttachPin(pin_definition.pwm1_pin, 0);    // 将IO3连接到通道0
    ledcSetup(1, OUTPUT_FREQ, OUTPUT_RESOLUTION); // 通道1，频率1kHz，12位分辨率
    ledcAttachPin(pin_definition.pwm2_pin, 1);    // 将IO4连接到通道1
}

void loop()
{
    // digitalWrite(pin_definition.led_pin, HIGH); // 点亮LED
    // delay(100);                                 // LED闪烁持续时间
    // digitalWrite(pin_definition.led_pin, LOW);  // 熄灭LED
    // delay(100);                                 // LED熄灭持续时间

    // 读取SBUS信号
    if (sbus_rx.Read())
    {
        sbus_data = sbus_rx.data();

        int pwm1_ch = map(valueClamp(sbus_data.ch[12], ch_min, ch_max), ch_min, ch_max, TARGET_MIN, TARGET_MAX);
        int pwm2_ch = map(valueClamp(sbus_data.ch[13], ch_min, ch_max), ch_min, ch_max, TARGET_MIN, TARGET_MAX);

        pwm1_ch = applyDeadZone(pwm1_ch);
        pwm2_ch = applyDeadZone(pwm2_ch);

        // 将13、14通道的值映射到0-255范围
        pwm1_value = map(pwm1_ch, TARGET_MIN, TARGET_MAX, 0, ((1 << OUTPUT_RESOLUTION) - 1)) * PWM_DUTY; // 通道13
        pwm2_value = map(pwm2_ch, TARGET_MIN, TARGET_MAX, 0, ((1 << OUTPUT_RESOLUTION) - 1)) * PWM_DUTY; // 通道14

        // 打印通道值和对应的PWM输出
        // SerialPrint.print("Channel 13 Value: "); // 打印通道13的值
        // SerialPrint.print(sbus_data.ch[12]);     // 打印通道13的原始值
        // SerialPrint.print(", PWM Output 1: ");   // 打印PWM输出1
        // SerialPrint.println(pwm1_value);         // 打印映射后的PWM值

        // SerialPrint.print("Channel 14 Value: "); // 打印通道14的值
        // SerialPrint.print(sbus_data.ch[13]);     // 打印通道14的原始值
        // SerialPrint.print(", PWM Output 2: ");   // 打印PWM输出2
        // SerialPrint.println(pwm2_value);         // 打印映射后的PWM值
    }
    // 输出PWM信号
    ledcWrite(0, pwm1_value); // 输出PWM信号到IO3
    ledcWrite(1, pwm2_value); // 输出PWM信号到IO4
    delay(20);
}