#include <Arduino.h>
#include <HardwareSerial.h>
#include "sbus.h"
#include <IBusBM.h>

typedef struct
{
    int8_t sbus_rx_pin;
    int8_t sbus_tx_pin;
    int8_t ibus_rx_pin;
    int8_t ibus_tx_pin;
    int8_t led_pin;
} PinDefinition;

// 结构体实例化
PinDefinition pin_definition = {
    .sbus_rx_pin = 7,
    .sbus_tx_pin = 6,
    .ibus_rx_pin = 4,  // IBUS输出引脚
    .ibus_tx_pin = 3,  // IBUS输出引脚
    .led_pin = 8
};

HardwareSerial SerialPrint(0);
HardwareSerial SerialSbus(1);
HardwareSerial SerialIbus(2);

// SBUS相关常量
const int ch_max = 1630;
const int ch_min = 372;
const int ch_mid = (ch_max - ch_min) / 2 + ch_min;

// IBUS相关常量
const int IBUS_MIN = 1000;
const int IBUS_MAX = 2000;
const int IBUS_MID = (IBUS_MAX - IBUS_MIN) / 2 + IBUS_MIN;

bfs::SbusRx sbus_rx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusData sbus_data;

IBusBM IBusSensor;

// 通道数据存储
uint16_t ibus_channels[10] = {IBUS_MID, IBUS_MID, IBUS_MID, IBUS_MID, IBUS_MID, 
                              IBUS_MID, IBUS_MID, IBUS_MID, IBUS_MID, IBUS_MID};

// 限幅函数
int valueClamp(int value, int minValue, int maxValue)
{
    if (value < minValue)
    {
        return minValue;
    }
    else if (value > maxValue)
    {
        return maxValue;
    }
    else
    {
        return value;
    }
}

// SBUS值转换为IBUS值
uint16_t sbusToIbus(uint16_t sbusValue)
{
    // 将SBUS值(372-1630)映射到IBUS值(1000-2000)
    return map(valueClamp(sbusValue, ch_min, ch_max), ch_min, ch_max, IBUS_MIN, IBUS_MAX);
}

// 发送IBUS数据包
void sendIbusPacket()
{
    uint8_t ibusPacket[32];
    uint16_t checksum = 0xFFFF;
    
    // IBUS包头
    ibusPacket[0] = 0x20;  // IBUS命令字节
    ibusPacket[1] = 0x40;  // IBUS命令字节
    
    // 添加通道数据（小端序）
    for (int i = 0; i < 10; i++)
    {
        ibusPacket[2 + i * 2] = ibus_channels[i] & 0xFF;        // 低字节
        ibusPacket[2 + i * 2 + 1] = (ibus_channels[i] >> 8) & 0xFF; // 高字节
        
        // 计算校验和
        checksum -= ibusPacket[2 + i * 2];
        checksum -= ibusPacket[2 + i * 2 + 1];
    }
    
    // 添加校验和（小端序）
    ibusPacket[22] = checksum & 0xFF;
    ibusPacket[23] = (checksum >> 8) & 0xFF;
    
    // 发送数据包
    SerialIbus.write(ibusPacket, 24);
}

void setup()
{
    /* Serial to display data */
    SerialPrint.begin(115200, SERIAL_8N1, -1, -1);
    pinMode(pin_definition.led_pin, OUTPUT);

    /* Begin the SBUS communication */
    sbus_rx.Begin();

    /* Begin the IBUS communication for output */
    SerialIbus.begin(115200, SERIAL_8N1, pin_definition.ibus_rx_pin, pin_definition.ibus_tx_pin);

    SerialPrint.println("SBUS to IBUS Converter Started");
    SerialPrint.println("Reading SBUS and converting to IBUS...");
}

void loop()
{
    static unsigned long lastIbusUpdate = 0;
    static unsigned long lastPrint = 0;
    static bool ledState = false;
    
    // 读取SBUS信号
    if (sbus_rx.Read())
    {
        sbus_data = sbus_rx.data();
        
        // 转换前10个通道的SBUS数据到IBUS格式
        for (int i = 0; i < 10; i++)
        {
            ibus_channels[i] = sbusToIbus(sbus_data.ch[i]);
        }
        
        // LED指示数据接收
        ledState = !ledState;
        digitalWrite(pin_definition.led_pin, ledState);
    }
    
    // 每7ms发送一次IBUS数据包（IBUS标准频率约为143Hz）
    if (millis() - lastIbusUpdate >= 7)
    {
        lastIbusUpdate = millis();
        sendIbusPacket();
    }
    
    // 每500ms打印一次调试信息
    if (millis() - lastPrint >= 500)
    {
        lastPrint = millis();
        
        SerialPrint.print("SBUS->IBUS: ");
        for (int i = 0; i < 6; i++) // 只打印前6个通道以节省空间
        {
            SerialPrint.print("CH");
            SerialPrint.print(i + 1);
            SerialPrint.print(":");
            SerialPrint.print(sbus_data.ch[i]);
            SerialPrint.print("->");
            SerialPrint.print(ibus_channels[i]);
            SerialPrint.print(" ");
        }
        SerialPrint.println();
    }
    
    delay(1);
}
