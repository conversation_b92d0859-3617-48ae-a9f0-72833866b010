.pio/build/sbus_to_ibus/FrameworkArduino/WMath.cpp.o: \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WMath.cpp \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_system/include/esp_system.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_common/include/esp_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_common/include/esp_attr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_common/include/esp_bit_defs.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_common/include/esp_idf_version.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/include/esp_mac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/include/esp_chip_info.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/include/esp_random.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_timer/include/esp_timer.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/log/include/esp_log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_rom/include/esp_rom_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/soc/esp32c3/include/soc/reset_reasons.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_rom/include/esp32c3/rom/ets_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/log/include/esp_log_internal.h
