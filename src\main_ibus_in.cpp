#include <Arduino.h>
#include <HardwareSerial.h>
#include <IBusBM.h>

typedef struct
{
    int8_t ibus_rx_pin;
    int8_t ibus_tx_pin;
    int8_t led_pin;
    int8_t pwm1_pin;
    int8_t pwm2_pin;
} PinDefinition;

// 结构体实例化
PinDefinition pin_definition = {
    .ibus_rx_pin = 7,
    .ibus_tx_pin = 6,
    .led_pin = 8,
    .pwm1_pin = 3,
    .pwm2_pin = 4
};

// 定义常量
const int OUTPUT_FREQ = 1000;     // 输出PWM频率（Hz）
const int OUTPUT_RESOLUTION = 12; // 输出PWM分辨率（位）
const float PWM_DUTY = 0.45;      // 占空比

HardwareSerial SerialPrint(0);
HardwareSerial SerialIbus(1);

const int TARGET_MIN = 1000;
const int TARGET_MAX = 2000;
const int TARGET_MID = (TARGET_MAX - TARGET_MIN) / 2 + TARGET_MIN;
const int DEAD_ZONE = 10; // 中位死区

IBusBM IBus;    // IBus对象

int pwm1_value = 0;
int pwm2_value = 0;

// 限幅函数
int valueClamp(int value, int minValue, int maxValue)
{
    if (value < minValue)
    {
        return minValue;
    }
    else if (value > maxValue)
    {
        return maxValue;
    }
    else
    {
        return value;
    }
}

// 定义一个函数来处理通道值的死区
int applyDeadZone(int pwm_ch)
{
    if (pwm_ch < TARGET_MIN + DEAD_ZONE)
    {
        return TARGET_MIN; // 如果小于死区，返回最小值
    }
    return pwm_ch; // 否则返回原值
}

void setup()
{
    /* Serial to display data */
    SerialPrint.begin(115200, SERIAL_8N1, -1, -1); // RX, TX
    pinMode(pin_definition.led_pin, OUTPUT); // 设置LED引脚为输出模式

    /* Begin the IBUS communication */
    IBus.begin(SerialIbus, IBUSBM_NOTIMER, pin_definition.ibus_rx_pin, pin_definition.ibus_tx_pin);

    // 初始化LEDC
    ledcSetup(0, OUTPUT_FREQ, OUTPUT_RESOLUTION); // 通道0，频率1kHz，12位分辨率
    ledcAttachPin(pin_definition.pwm1_pin, 0);    // 将IO3连接到通道0
    ledcSetup(1, OUTPUT_FREQ, OUTPUT_RESOLUTION); // 通道1，频率1kHz，12位分辨率
    ledcAttachPin(pin_definition.pwm2_pin, 1);    // 将IO4连接到通道1

    SerialPrint.println("IBUS Reader Started");
    SerialPrint.println("Waiting for IBUS data...");
}

void loop()
{
    // 调用IBus的内部循环函数
    IBus.loop();

    // 读取IBUS信号
    static unsigned long lastPrint = 0;
    if (millis() - lastPrint > 100) // 每100ms打印一次
    {
        lastPrint = millis();
        
        // 打印所有通道的值
        SerialPrint.print("IBUS Channels: ");
        for (int i = 0; i < 10; i++) // IBUS通常有10个通道
        {
            int channelValue = IBus.readChannel(i);
            SerialPrint.print("CH");
            SerialPrint.print(i + 1);
            SerialPrint.print(":");
            SerialPrint.print(channelValue);
            SerialPrint.print(" ");
        }
        
        // 打印接收计数器
        SerialPrint.print(" | RX Count: ");
        SerialPrint.print(IBus.cnt_rec);
        SerialPrint.println();

        // 使用通道1和通道2控制PWM输出（示例）
        int ch1_value = IBus.readChannel(0); // 通道1
        int ch2_value = IBus.readChannel(1); // 通道2
        
        if (ch1_value > 0) // 确保接收到有效数据
        {
            // 将通道值映射到PWM输出
            pwm1_value = map(valueClamp(ch1_value, TARGET_MIN, TARGET_MAX), TARGET_MIN, TARGET_MAX, 0, ((1 << OUTPUT_RESOLUTION) - 1)) * PWM_DUTY;
            pwm2_value = map(valueClamp(ch2_value, TARGET_MIN, TARGET_MAX), TARGET_MIN, TARGET_MAX, 0, ((1 << OUTPUT_RESOLUTION) - 1)) * PWM_DUTY;
            
            // 输出PWM信号
            ledcWrite(0, pwm1_value); // 输出PWM信号到IO3
            ledcWrite(1, pwm2_value); // 输出PWM信号到IO4
            
            // LED指示有数据接收
            digitalWrite(pin_definition.led_pin, HIGH);
        }
        else
        {
            // 没有有效数据时关闭LED
            digitalWrite(pin_definition.led_pin, LOW);
        }
    }
    
    delay(1); // 短暂延时
}
