// AUTOMATICALLY GENERATED FILE. PLEASE DO NOT MODIFY IT MANUALLY
//
// PlatformIO Debugging Solution
//
// Documentation: https://docs.platformio.org/en/latest/plus/debugging.html
// Configuration: https://docs.platformio.org/en/latest/projectconf/sections/env/options/debug/index.html

{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug",
            "executable": "C:/zhangshun/PlatformIO/Projects/ESP32_C3_SBUS_IBUS/.pio/build/sbus_to_ibus/firmware.elf",
            "projectEnvName": "sbus_to_ibus",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "preLaunchTask": {
                "type": "PlatformIO",
                "task": "Pre-Debug (sbus_to_ibus)"
            }
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (skip Pre-Debug)",
            "executable": "C:/zhangshun/PlatformIO/Projects/ESP32_C3_SBUS_IBUS/.pio/build/sbus_to_ibus/firmware.elf",
            "projectEnvName": "sbus_to_ibus",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin",
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (without uploading)",
            "executable": "C:/zhangshun/PlatformIO/Projects/ESP32_C3_SBUS_IBUS/.pio/build/sbus_to_ibus/firmware.elf",
            "projectEnvName": "sbus_to_ibus",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "loadMode": "manual"
        }
    ]
}
